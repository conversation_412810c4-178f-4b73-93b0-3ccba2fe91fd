const { Configuration } = require('webpack')
const { DllPlugin } = require('webpack')
const path = require('path')
/**
 * @type {Configuration}
 */
const config = {
    mode: 'development',
    entry: {
        vue: ['vue', 'pinia']
    },
    output:{
        filename: '[name].dll.js',
        path: path.resolve(__dirname, 'dll'),
    },
    plugins: [
        new DllPlugin({
            name: '[name]',
            path: path.resolve(__dirname, 'dll', 'manifest.json')
        })
    ]
}

module.exports = config