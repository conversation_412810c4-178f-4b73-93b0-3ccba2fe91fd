const { Configuration } = require('webpack')
const { DllReferencePlugin } = require('webpack')
const path = require('path')
/**
 * @type {Configuration}
 */
const config = {
    mode: 'development',
    entry:'./main.js',
    output:{
        filename:'bundle.js',
        path:path.resolve(__dirname,'dist'),
    },
    plugins:[
        new DllReferencePlugin({
            manifest:path.resolve(__dirname,'dll','manifest.json')
        })
    ]
}

module.exports = config