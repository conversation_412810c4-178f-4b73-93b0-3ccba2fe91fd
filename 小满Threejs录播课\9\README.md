# 性能优化

Web Vitals 技术

- LCP(最大内容绘制) 标记绘制首个文本或者是图片的时间
- FCP(首次内容绘制) 标记绘制最大文本或者图片的时间
- TBT(阻塞主线程) 统计任务超过`50毫秒`的时间 统计长任务
- CLS(累积布局偏移) 动态插入广告 动态加载内容 以及移动端布局适配
- SI(累积布局偏移) 标记布局偏移的累积时间


RAIL 模型 性能优化指标用户

- Response 响应时间
- Animation 动画时间
- Idle 空闲时间
- Load 加载时间


识别大型脚本

```html
 <script>
        //识别大型脚本
        //监控性能的API
        new PerformanceObserver((entryList) => {
            const entrys = entryList.getEntries()
            entrys.forEach(entry => {
                if (entry.initiatorType === 'script') {
                    const startTime = performance.now()
                    window.requestAnimationFrame(() => {
                        const endTime = performance.now()
                        const duration = endTime - startTime
                        //如果大于50ms 则认为是长任务
                        if (duration > 50) {
                            console.log('长任务', entry.name, duration)
                        } else {
                            console.log('短任务', entry.name, duration)
                        }
                    })
                }
            })
        }).observe({ entryTypes: ['resource'] })
        //resource监听资源
    </script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.16/dist/vue.js"></script>
```

为什么要用requestAnimationFrame去算

是因为浏览器一帧做了什么事情?
16毫秒 14毫秒
1. 处理用户的交互事件,click,touch,scroll
2. 执行宏任务(PerformanceObserver)
3. 执行dom的回流与重绘
4. 计算domUI像素
5. 合并计算机视图指令运行到CPU
6. requestIdleCallback(他不是一定执行的，如果时间充足，则执行，如果时间不充足，则不执行)
7. 执行requestAnimationFrame的回调(结尾)


关于图片优化(avif)技术

jpg -> png -> apng -> webp -> avif

intersectionObserver 懒加载 

<img  src='xxxx.com' data-url='真实的图片.com'>

new intersectionObserver((entries) => {
    entries.forEach(entry => {
        if(entry.isIntersecting){
            const img = entry.target
            img.src = img.dataset.url
            observer.unobserve(img)
        }
    })
}).observe(img)


async 不阻塞DOM解析时间了可以并行 加载时间不稳定
defer 不阻塞DOM 并且他只是在DOM解析完成之后执行 稳定的
可以并行加载了


# 四大天王

1. 预取回prefetch
2. 预加载preload
3. DNS预取回dns-prefetch (为了给IE用的现在IE废弃了这个也废弃了)
4. 预取回preconnect

```html
<link rel="prefetch" href="https://static.zhihu.com/heifetz/6116.216a26f4.7e059bd26c25b9e701c1.css">
<link as="font" rel="preload" href="https://lf3-cdn-tos.bytegoofy.com/obj/goofy/toutiao/toutiao_web_pc/fonts/Byte_Number-center.688369de.ttf">
```

preconnect 提前把指定资源的DNS寻址完成 TCP连接什么的都完成了


浏览器输入URL发生了事情

1. DNS解析
 - 从本地etc目录寻找DNS地址
 - 从本地host文件寻找DNS地址
 - 从浏览器缓存中寻找DNS地址
    - 网络DNS寻址 UDP协议默认协议 也能改TCP
    - 递归查找
    - .根域名查找
    - .com.顶级域名查找
    - baidu.com.权威域名查找
2. 建立TCP连接
   三次握手
   A-B ACK标记第一次握手 B会给A回馈一个seq = 0 
   A就会通过seq + 1 校验第二次是否正确 seq + ack 发给B
   B再去校验seq + 1 是否正确 如果正确 则建立连接
3. 缓存
  - 强缓存
  - 协商缓存
4. 发送HTTP请求
  - 发送请求行
  - 发送请求头
  - 发送请求体
5. 服务器处理请求


网络优化

- http1.1 http2 http3 UDP协议 + quick算法

http2
- 二进制分帧层
- 多路复用
- 头部压缩
- 服务器推送 (没有了) http push

CDN优化

主从服务器 主服务器是北京 从服务器 河南 天津 上海 甘肃
杭州访问 ->就近访问上海

杭州->上海





DOM性能优化

1. 文档碎片DocumentFragment
2. 虚拟列表
3. 减少回流与重绘

treeShaking自动的webpack5自动开启 vite rollup自带的

const a = () => {
    return 'a'
}
let x = 3
import {a,b} from './a.js'

b()


工程化

 - 代码分包(懒加载)
 - 代码压缩

垃圾回收机制

- 标记清除
- 引用计数

let a = 1
let b = a

案例 检测卡顿


webpack 5.101.0 compiled with 1 warning in 529 ms
webpack 5.101.0 compiled with 1 warning in 96 ms